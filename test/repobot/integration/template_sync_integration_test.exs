defmodule Repobot.Integration.TemplateSyncIntegrationTest do
  @moduledoc """
  Integration tests that verify the complete end-to-end flow of template repository
  synchronization from push event to target repository sync completion.
  """

  use Repobot.DataCase, async: false
  use Oban.Testing, repo: Repobot.Repo
  use Repobot.Test.Fixtures

  import Mox
  import Ecto.Query

  alias Repobot.{Events, Repo}
  alias Repobot.Events.Event
  alias Repobot.Workers.EventHandlers.GitHub.Push
  alias Repobot.Workers.EventHandlers.Sync

  setup :set_mox_from_context
  setup :verify_on_exit!

  setup do
    user = create_user()
    %{user: user}
  end

  describe "complete template sync flow" do
    test "push to template repository triggers sync to multiple target repositories", %{
      user: user
    } do
      # Create template repository
      template_repo =
        create_repository(%{
          template: true,
          name: "template-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create multiple target repositories
      target_repo_1 =
        create_repository(%{
          name: "target-repo-1",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      target_repo_2 =
        create_repository(%{
          name: "target-repo-2",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      target_repo_3 =
        create_repository(%{
          name: "target-repo-3",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create multiple source files
      config_file =
        create_source_file(%{
          name: "config.ex.liquid",
          target_path: "config/config.ex",
          content: "old config content",
          is_template: true,
          source_repository_id: template_repo.id,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      readme_file =
        create_source_file(%{
          name: "README.md",
          target_path: "README.md",
          content: "old readme content",
          source_repository_id: template_repo.id,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Associate source files with all repositories
      for source_file <- [config_file, readme_file] do
        for repo <- [template_repo, target_repo_1, target_repo_2, target_repo_3] do
          Repo.insert!(%Repobot.RepositorySourceFile{
            repository_id: repo.id,
            source_file_id: source_file.id
          })
        end
      end

      # Create push event payload
      payload = %{
        "ref" => "refs/heads/main",
        "repository" => %{
          "full_name" => template_repo.full_name,
          "default_branch" => "main"
        },
        "commits" => [
          %{
            "id" => "abc123",
            "message" => "Update template files",
            "added" => [],
            "modified" => ["config/config.ex.liquid", "README.md"]
          }
        ]
      }

      # Create the push event
      {:ok, push_event} =
        Events.create_event(%{
          type: "github.push",
          payload: payload,
          organization_id: user.default_organization_id,
          repository_id: template_repo.id,
          status: "pending"
        })

      # Mock GitHub API calls for push handler and sync handlers
      # Push handler: 4 calls (1 template + 3 targets)
      # Sync handlers: 3 calls (1 per target repo)
      # Total: 7 calls
      Repobot.Test.GitHubMock
      |> expect(:client, 7, fn owner, repo ->
        cond do
          owner == template_repo.owner and repo == template_repo.name -> :template_client
          owner == target_repo_1.owner and repo == target_repo_1.name -> :target_client_1
          owner == target_repo_2.owner and repo == target_repo_2.name -> :target_client_2
          owner == target_repo_3.owner and repo == target_repo_3.name -> :target_client_3
        end
      end)
      |> expect(:client, fn _user -> :user_client end)
      |> expect(:get_file_content, 2, fn :template_client, owner, repo, path, commit_sha ->
        if owner == template_repo.owner and repo == template_repo.name and commit_sha == "abc123" do
          case path do
            "config/config.ex.liquid" -> {:ok, "new config content", %{"sha" => "new-sha-1"}}
            "README.md" -> {:ok, "new readme content", %{"sha" => "new-sha-2"}}
            _ -> raise "Unexpected path: #{path}"
          end
        else
          raise "Unexpected get_file_content call: #{inspect({owner, repo, path, commit_sha})}"
        end
      end)
      |> expect(:get_file_content, 2, fn :user_client, owner, repo_name, path ->
        if owner == template_repo.owner and repo_name == template_repo.name do
          case path do
            "config/config.ex.liquid" ->
              {:ok, "new config content", %{"sha" => "abc123", "size" => 18}}

            "README.md" ->
              {:ok, "new readme content", %{"sha" => "abc123", "size" => 18}}

            _ ->
              {:error, "not found"}
          end
        else
          {:error, "not found"}
        end
      end)

      # Mock sync backend for all target repositories
      # Use stub to allow any number of calls
      Repobot.Test.SyncMock
      |> stub(:sync_changes, fn source_files, template_repo_arg, target_repo_arg, client, opts ->
        # Verify arguments
        assert length(source_files) == 2
        source_file_ids = Enum.map(source_files, & &1.id)
        assert config_file.id in source_file_ids
        assert readme_file.id in source_file_ids
        assert template_repo_arg.id == template_repo.id
        assert target_repo_arg.id in [target_repo_1.id, target_repo_2.id, target_repo_3.id]
        assert client in [:target_client_1, :target_client_2, :target_client_3]
        assert opts[:commit_message] == "Update template files"

        {:ok, "Files synced successfully to #{target_repo_arg.name}"}
      end)

      # Step 1: Process the push event
      assert :ok = perform_job(Push, %{"event_id" => push_event.id})

      # Verify push event was processed (check for worker success event)
      push_worker_events =
        Repo.all(
          from e in Event,
            where:
              e.type == "repobot.worker.success" and
                fragment("?->>'original_event_type' = ?", e.payload, "github.push")
        )

      assert length(push_worker_events) >= 1

      # Verify source files were updated
      updated_config_file = Repo.get(Repobot.SourceFile, config_file.id)
      assert updated_config_file.content == "new config content"

      updated_readme_file = Repo.get(Repobot.SourceFile, readme_file.id)
      assert updated_readme_file.content == "new readme content"

      # Step 2: Verify sync events were created for all target repositories
      sync_events = Repo.all(from e in Event, where: e.type == "repobot.sync")

      original_sync_events =
        Enum.filter(sync_events, fn sync_event ->
          Map.has_key?(sync_event.payload, "triggered_by_event_id")
        end)

      assert length(original_sync_events) == 3

      # Verify all target repositories have sync events
      target_repo_ids =
        Enum.map(original_sync_events, fn event ->
          event.payload["target_repository_id"]
        end)

      assert target_repo_1.id in target_repo_ids
      assert target_repo_2.id in target_repo_ids
      assert target_repo_3.id in target_repo_ids

      # Step 3: Process all sync events
      for sync_event <- original_sync_events do
        assert :ok = perform_job(Sync, %{"event_id" => sync_event.id})

        # Verify sync event was processed (check for worker success event)
        sync_worker_events =
          Repo.all(
            from e in Event,
              where:
                e.type == "repobot.worker.success" and
                  fragment("?->>'original_event_type' = ?", e.payload, "repobot.sync")
          )

        assert length(sync_worker_events) >= 1
      end

      # Step 4: Verify sync result events were created
      sync_success_events = Repo.all(from e in Event, where: e.type == "repobot.sync.success")

      # Should have sync success events for each target repository
      assert length(sync_success_events) >= 3

      # Verify all sync operations completed successfully by checking sync success events
      assert length(sync_success_events) >= 3
    end
  end
end
